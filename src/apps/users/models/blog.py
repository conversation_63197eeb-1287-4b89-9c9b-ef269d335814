from django.db import models
from common.models.base_model import BaseModel

class Blog(BaseModel):
    title = models.CharField(
        max_length=200,
        verbose_name="Blog Title",
        help_text="Enter the title of the blog post"
    )
    content = models.TextField(
        verbose_name="Content",
        help_text="Enter the main content of the blog post"
    )
    author = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='blogs',
        verbose_name="Author",
        help_text="The user who wrote this blog post"
    )
    image_url =models.CharField(
        verbose_name="blog image"
    )
    class Meta:
        db_table = 'blogs_blog'
        verbose_name = "Blog"
        verbose_name_plural = "Blogs"
        ordering = ['-created_at'] 

    def __str__(self):
        return f"{self.title} by {self.author.username}"
