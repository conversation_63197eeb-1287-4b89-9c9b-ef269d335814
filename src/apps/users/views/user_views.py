import logging
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSONPars<PERSON>
from rest_framework.permissions import AllowAny
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiExample
from ..exceptions.exceptions import UserException
from ..serializers.user_serializers import UserRegisterSerializer,UserModifySerializer

User = get_user_model()
logger = logging.getLogger(__name__)


class RegisterView(generics.CreateAPIView):
    parser_classes = (<PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser, MultiPartParser)
    queryset = User.objects.all()
    serializer_class = UserRegisterSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except UserException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in user registration: {str(e)}")
            raise UserException(
                message="An unexpected error occurred",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def create(self, request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.save()
        headers = self.get_success_headers(serializer.data)

        logger.info(f"User registered successfully: {user.username}")

        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )


class ModifyView(generics.CreateAPIView):
    parser_classes = (JSONParser, MultiPartParser)
    queryset = User.objects.all()
    serializer_class = UserModifySerializer
    permission_classes = [AllowAny]
    def Put(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except UserException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in user registration: {str(e)}")
            raise UserException(
                message="An unexpected error occurred",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def create(self, request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.save()
        headers = self.get_success_headers(serializer.data)

        logger.info(f"User modify successfully: {user.username}")

        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )

