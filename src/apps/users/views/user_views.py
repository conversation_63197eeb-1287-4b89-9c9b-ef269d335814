import logging
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON>ars<PERSON>
from rest_framework.permissions import AllowAny
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiExample
from ..exceptions.exceptions import UserException
from ..serializers.user_serializers import UserRegisterSerializer,UserModifySerializer,GetAllUsersSerializer
from ..services.user_service import UserService

User = get_user_model()
logger = logging.getLogger(__name__)


class RegisterView(generics.CreateAPIView):
    parser_classes = (<PERSON><PERSON><PERSON>ars<PERSON>, FormParser, MultiPartParser)
    queryset = User.objects.all()
    serializer_class = UserRegisterSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except UserException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in user registration: {str(e)}")
            raise UserException(
                message="An unexpected error occurred",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def create(self, request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.save()
        headers = self.get_success_headers(serializer.data)

        logger.info(f"User registered successfully: {user.username}")

        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )


class ModifyView(generics.CreateAPIView):
    parser_classes = (FormParser, MultiPartParser)
    queryset = User.objects.all()
    serializer_class = UserModifySerializer
    permission_classes = [AllowAny]
    def Post(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except UserException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in user registration: {str(e)}")
            raise UserException(
                message="An unexpected error occurred",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def modify(self, request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.save()
        headers = self.get_success_headers(serializer.data)

        logger.info(f"User modify successfully: {user.username}")

        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )


class GetAllUsersView(generics.ListAPIView):
    serializer_class = GetAllUsersSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        include_inactive = self.request.query_params.get('include_inactive', 'false').lower() == 'true'
        return UserService.get_all_users(include_inactive=include_inactive)

    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            logger.info(f"Retrieved {len(serializer.data)} users")

            return Response({
                'count': len(serializer.data),
                'users': serializer.data
            }, status=status.HTTP_200_OK)

        except UserException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in getting all users: {str(e)}")
            raise UserException(
                message="An unexpected error occurred while retrieving users",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    pagination_class = None

