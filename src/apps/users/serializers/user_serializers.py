import logging
import re
from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError as DjangoValidationError
from drf_spectacular.utils import extend_schema_field
from rest_framework_simplejwt.tokens import RefreshToken

from ..services.user_service import UserService


User = get_user_model()
logger = logging.getLogger(__name__)

class UserRegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True,
        min_length=8,
        help_text="Password must be at least 8 characters long"
    )
    password_confirm = serializers.CharField(
        write_only=True,
        help_text="Confirm your password"
    )
    class Meta:
        model = User
        fields = [
            "username", "email",
            "password", "password_confirm"
        ]
        extra_kwargs = {
            'username': {'required': True, 'allow_blank': False},
            'email': {'required': True, 'allow_blank': False},
        }
        
    def create(self, validated_data):
        username = validated_data.get("username")
        email = validated_data.get("email")
        password = validated_data.get("password")
        user = UserService.create_user(
            username=username,
            email=email,
            password=password,
        )
        self.refresh=RefreshToken.for_user(user)
        return user
    def to_representation(self, instance):
        refresh = getattr(self, "refresh", None)
        data = {
            'user_id':instance.id,
            "username": instance.username,
            "email": instance.email,
        }
        if refresh:
            data["access_token"] = str(refresh.access_token)
            data["refresh_token"] = str(refresh)
        return data
        
    
class UserModifySerializer(serializers.ModelSerializer):
        image = serializers.ImageField(
        required=False,
        allow_null=True,
        help_text="Profile image (optional)"
        )
        image = serializers.ImageField(required=False, allow_null=True)
        image_modifided = serializers.BooleanField(write_only=True, required=False,default=False)
        id = serializers.UUIDField(required=True)
        class Meta :
            model=User
            fields=["id","first_name","last_name","username","email","image_modifided","image"]
            extra_kwargs = {
                'username': {'required': True, 'allow_blank': False},
                'email': {'required': True, 'allow_blank': False},
                'first_name':{'required':True},
                'last_name':{'required':True}
            }
        def modify(self ,validated_data):
            user_id=validated_data.get('id')
            first_name=validated_data.get('first_name')
            last_name=validated_data.get('last_name')
            username=validated_data.get('username')
            email=validated_data.get('email')
            image=validated_data.get('image')
            image_modifided=validated_data.get('image_modifided')
            try:
                UserService.modify_user(user_id,first_name,last_name,username,email,image_modifided,image)
            except Exception as e:
                raise e
        
        def to_representation(self, instance):
            data = {
                "id":instance.id,
                "first_name":instance.first_name,
                "last_name":instance.last_name,
                "username": instance.username,
                "email": instance.email,
                "image_url":instance.image_url,
            }
            return data
            
class GetAllUsersSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'image_url', 'phone_number', 'date_joined', 'is_active'
        ]
        read_only_fields = ['id', 'date_joined']

    def to_representation(self, instance):
        data = {
            'id': instance.id,
            'username': instance.username,
            'email': instance.email,
            'first_name': instance.first_name,
            'last_name': instance.last_name,
            'full_name': instance.full_name,
            'image_url': instance.image_url,
            'phone_number': instance.phone_number,
            'date_joined': instance.date_joined,
            'is_active': instance.is_active,
        }
        return data


